# Clerk Billing Implementation Plan

## ✅ Phase 1: Setup & Configuration
- [ ] 1.1 Enable Clerk billing in dashboard (Manual step)
- [ ] 1.2 Configure Stripe integration (Manual step)
- [x] 1.3 Add environment variables
- [ ] 1.4 Update session token claims (Manual step)

## ✅ Phase 2: Create Billing Plans in Clerk Dashboard
- [ ] 2.1 Create "Reply Guy" plan ($9/month) (Manual step)
- [ ] 2.2 Create "Reply God" plan ($29/month) (Manual step)
- [ ] 2.3 Create "Team Plan" plan ($99/month) (Manual step)
- [ ] 2.4 Add features to each plan (Manual step)

## ✅ Phase 3: Frontend Implementation
- [x] 3.1 Create pricing page with PricingTable component
- [x] 3.2 Update profile page billing section
- [x] 3.3 Add subscription management UI
- [x] 3.4 Implement plan upgrade/downgrade flows

## ✅ Phase 4: Backend Integration
- [x] 4.1 Update webhook handlers for billing events
- [x] 4.2 Modify user service to use Clerk billing
- [x] 4.3 Update feature access checks
- [x] 4.4 Add billing-related tRPC routes

## ✅ Phase 5: Database Migration
- [x] 5.1 Update database schema for Clerk billing
- [ ] 5.2 Migrate existing users to Clerk plans (Pending DB access)
- [ ] 5.3 Update seed scripts

## ✅ Phase 6: Testing & Validation
- [ ] 6.1 Test subscription flows
- [ ] 6.2 Test feature access controls
- [ ] 6.3 Test webhook handling
- [ ] 6.4 Validate billing integration

## Current Status: Phase 3-4 Complete, Ready for Manual Setup

### ✅ What's Been Implemented:

**Database Schema:**
- ✅ Added Clerk billing columns to users table via Supabase
- ✅ Fields: clerkPlanId, clerkPlanName, subscriptionStatus, subscriptionUpdatedAt

**Frontend Components:**
- ✅ Created `/pricing` page with PricingTable component
- ✅ Updated profile page with Clerk billing UI
- ✅ Added pricing link to navigation
- ✅ Responsive design with feature comparison tables

**Backend Integration:**
- ✅ Updated webhook handlers for billing events
- ✅ Created billing tRPC router with all necessary endpoints
- ✅ Added Clerk billing feature access checks
- ✅ Created migration script for existing users

**Developer Tools:**
- ✅ Created comprehensive setup guide
- ✅ Added test files for billing functionality
- ✅ Added npm scripts for migration

### 🔧 Next Steps (Manual):

1. **Enable Clerk Billing in Dashboard**
   - Go to Clerk Dashboard → Billing Settings
   - Enable billing feature
   - Connect Stripe or use development gateway

2. **Create Plans in Clerk Dashboard**
   - Create "reply-guy" plan ($9/month)
   - Create "reply-god" plan ($29/month)
   - Create "team-plan" plan ($99/month)
   - Add features to each plan

3. **Configure Webhooks**
   - Add billing webhook events to Clerk
   - Test webhook delivery

4. **Test Integration**
   - Visit `/pricing` page
   - Test subscription flows
   - Verify feature access controls

### 🎨 **UI/UX Improvements Completed:**

**Pricing Section Moved to Profile:**
- ✅ Removed pricing from main navigation
- ✅ Integrated into profile page billing section
- ✅ Enhanced with interactive animations
- ✅ Improved color theming for light/dark modes

**Interactive Features:**
- ✅ Hover effects and smooth transitions
- ✅ Expandable plan details on click
- ✅ Progress bars for usage tracking
- ✅ Gradient backgrounds for selected plans
- ✅ Responsive design for all screen sizes

**Theme Consistency:**
- ✅ Proper app color variables usage
- ✅ Dark mode support with correct contrasts
- ✅ Consistent spacing and typography
- ✅ Professional card layouts with shadows

## Implementation Details

### Phase 1 Tasks:
1. **Enable Clerk Billing**: Go to Clerk Dashboard → Billing Settings
2. **Stripe Setup**: Connect Stripe account or use development gateway
3. **Environment Variables**: Add billing-related env vars
4. **Session Claims**: Configure billing claims in session token

### Phase 2 Tasks:
1. **Plan Creation**: Create plans in Clerk Dashboard with exact pricing
2. **Feature Mapping**: Map current features to Clerk billing features
3. **Plan Configuration**: Set limits and permissions per plan

### Phase 3 Tasks:
1. **Pricing Page**: Implement `/pricing` route with PricingTable
2. **Profile Updates**: Replace custom billing UI with Clerk components
3. **Subscription Management**: Add upgrade/downgrade functionality

Let's start implementing!
