/**
 * Billing Router for BuddyChip
 * 
 * Handles Clerk billing integration and subscription management
 */

import { z } from 'zod';
import { createTRPCRouter, protectedProcedure } from '../lib/trpc';
import { TRPCError } from '@trpc/server';
import { prisma } from '../lib/db-utils';
import { canUserUseClerkFeature } from '../lib/user-service';
import { auth } from '@clerk/nextjs/server';

export const billingRouter = createTRPCRouter({
  /**
   * Get current user's subscription status
   */
  getSubscription: protectedProcedure
    .query(async ({ ctx }) => {
      console.log('🔍 BillingRouter: Getting subscription for user:', ctx.userId);

      try {
        const user = await prisma.user.findUnique({
          where: { id: ctx.userId },
          select: {
            // @ts-ignore - These fields exist in the database but not yet in generated types
            clerkPlanId: true,
            clerkPlanName: true,
            subscriptionStatus: true,
            subscriptionUpdatedAt: true,
            plan: {
              select: {
                id: true,
                name: true,
                displayName: true,
                description: true,
                price: true,
                features: true,
              },
            },
          },
        }) as any;

        if (!user) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'User not found',
          });
        }

        return {
          clerkPlan: {
            id: user.clerkPlanId,
            name: user.clerkPlanName,
            status: user.subscriptionStatus,
            updatedAt: user.subscriptionUpdatedAt,
          },
          legacyPlan: user.plan,
          isClerkBilling: !!user.clerkPlanName && user.subscriptionStatus === 'active',
        };
      } catch (error) {
        console.error('Error getting subscription:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to get subscription information',
        });
      }
    }),

  /**
   * Get usage information for current billing period
   */
  getUsage: protectedProcedure
    .query(async ({ ctx }) => {
      console.log('🔍 BillingRouter: Getting usage for user:', ctx.userId);

      try {
        const features = ['AI_CALLS', 'IMAGE_GENERATIONS', 'MONITORED_ACCOUNTS', 'MENTIONS_PER_MONTH', 'STORAGE_GB'];
        const usageData = [];

        for (const feature of features) {
          const usage = await canUserUseClerkFeature(ctx.userId, feature);
          usageData.push({
            feature,
            currentUsage: usage.currentUsage,
            limit: usage.limit,
            allowed: usage.allowed,
            resetDate: usage.resetDate,
          });
        }

        return usageData;
      } catch (error) {
        console.error('Error getting usage:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to get usage information',
        });
      }
    }),

  /**
   * Check if user can use a specific feature
   */
  canUseFeature: protectedProcedure
    .input(z.object({
      feature: z.enum(['AI_CALLS', 'IMAGE_GENERATIONS', 'MONITORED_ACCOUNTS', 'MENTIONS_PER_MONTH', 'STORAGE_GB', 'TEAM_MEMBERS']),
    }))
    .query(async ({ ctx, input }) => {
      console.log('🔍 BillingRouter: Checking feature usage for user:', ctx.userId, 'feature:', input.feature);

      try {
        return await canUserUseClerkFeature(ctx.userId, input.feature);
      } catch (error) {
        console.error('Error checking feature usage:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to check feature availability',
        });
      }
    }),

  /**
   * Get available plans for upgrade/downgrade
   */
  getAvailablePlans: protectedProcedure
    .query(async () => {
      console.log('🔍 BillingRouter: Getting available plans');

      try {
        // Return Clerk billing plan information
        // This would typically come from Clerk's API, but for now we'll return static data
        return [
          {
            id: 'reply-guy',
            name: 'Reply Guy',
            price: 9,
            currency: 'USD',
            interval: 'month',
            features: [
              'Basic AI replies',
              'Gemini 2.5 Flash model',
              '100 AI calls/month',
              '20 image generations',
              '3 monitored accounts',
              'Email support'
            ],
          },
          {
            id: 'reply-god',
            name: 'Reply God',
            price: 29,
            currency: 'USD',
            interval: 'month',
            features: [
              'Advanced AI replies',
              'Gemini 2.5 Pro model',
              '500 AI calls/month',
              '50 image generations',
              '10 monitored accounts',
              'Priority support',
              'Custom personas'
            ],
          },
          {
            id: 'team-plan',
            name: 'Team Plan',
            price: 99,
            currency: 'USD',
            interval: 'month',
            features: [
              'Everything in Reply God',
              'Unlimited AI calls',
              '100 image generations',
              '50 monitored accounts',
              'Team collaboration',
              'Admin dashboard',
              'Dedicated support',
              'Custom integrations'
            ],
          },
        ];
      } catch (error) {
        console.error('Error getting available plans:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to get available plans',
        });
      }
    }),

  /**
   * Get billing portal URL (for Clerk billing management)
   */
  getBillingPortalUrl: protectedProcedure
    .query(async ({ ctx }) => {
      console.log('🔍 BillingRouter: Getting billing portal URL for user:', ctx.userId);

      try {
        // In a real implementation, you would call Clerk's API to get the billing portal URL
        // For now, we'll return a placeholder
        return {
          url: `https://billing.clerk.com/portal?user_id=${ctx.userId}`,
          message: 'Billing portal integration coming soon',
        };
      } catch (error) {
        console.error('Error getting billing portal URL:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to get billing portal URL',
        });
      }
    }),

  /**
   * Check if user has access to a specific plan feature
   */
  hasAccess: protectedProcedure
    .input(z.object({
      plan: z.string().optional(),
      feature: z.string().optional(),
    }))
    .query(async ({ ctx, input }) => {
      console.log('🔍 BillingRouter: Checking access for user:', ctx.userId, 'input:', input);

      try {
        const { has } = await auth();
        
        if (input.plan) {
          return { hasAccess: has({ plan: input.plan }) };
        }
        
        if (input.feature) {
          return { hasAccess: has({ feature: input.feature }) };
        }

        return { hasAccess: false };
      } catch (error) {
        console.error('Error checking access:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to check access',
        });
      }
    }),
});
