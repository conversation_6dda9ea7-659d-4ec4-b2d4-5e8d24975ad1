'use client'

import { useState, useRef, useEffect } from 'react'
import { useUser, useClerk } from '@clerk/nextjs'
import { useTheme } from 'next-themes'
import { User, Settings, LogOut, ChevronDown, Sun, Moon, Monitor } from 'lucide-react'

export default function ProfileDropdown() {
  const { user, isLoaded } = useUser()
  const { signOut } = useClerk()
  const { theme, setTheme } = useTheme()
  const [isOpen, setIsOpen] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  if (!isLoaded || !user) {
    return (
      <a href="/sign-in" className="text-app-headline hover:text-app-main transition-colors flex items-center">
        <User className="w-4 h-4 mr-1 md:mr-2" /> LOGIN
      </a>
    )
  }

  const handleSignOut = () => {
    signOut()
    setIsOpen(false)
  }

  return (
    <div className="relative" ref={dropdownRef}>
      {/* Profile Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        onMouseEnter={() => setIsOpen(true)}
        className="text-app-headline hover:text-app-main transition-colors flex items-center space-x-2 group"
      >
        <div className="flex items-center space-x-2">
          <img 
            src={user.imageUrl} 
            alt="Profile" 
            className="w-6 h-6 md:w-8 md:h-8 rounded-full border border-app-stroke"
          />
          <span className="hidden md:block">
            {user.firstName || user.emailAddresses[0].emailAddress}
          </span>
          <ChevronDown className={`w-3 h-3 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
        </div>
      </button>

      {/* Dropdown Menu */}
      {isOpen && (
        <div 
          className="absolute right-0 mt-2 w-64 bg-app-card border border-app-stroke rounded-lg shadow-lg z-50"
          onMouseLeave={() => setIsOpen(false)}
        >
          {/* User Info Header */}
          <div className="px-4 py-3 border-b border-app-stroke">
            <div className="flex items-center space-x-3">
              <img 
                src={user.imageUrl} 
                alt="Profile" 
                className="w-10 h-10 rounded-full border border-app-stroke"
              />
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-app-headline truncate">
                  {user.firstName && user.lastName 
                    ? `${user.firstName} ${user.lastName}` 
                    : user.firstName || 'User'
                  }
                </p>
                <p className="text-xs text-app-headline opacity-70 truncate">
                  {user.emailAddresses[0]?.emailAddress}
                </p>
              </div>
            </div>
          </div>

          {/* Menu Items */}
          <div className="py-2">
            <a
              href="/profile"
              className="flex items-center px-4 py-2 text-sm text-app-headline hover:bg-app-background transition-colors"
              onClick={() => setIsOpen(false)}
            >
              <User className="w-4 h-4 mr-3" />
              View Profile
            </a>
            
            <a
              href="/dashboard"
              className="flex items-center px-4 py-2 text-sm text-app-headline hover:bg-app-background transition-colors"
              onClick={() => setIsOpen(false)}
            >
              <Settings className="w-4 h-4 mr-3" />
              Dashboard
            </a>

            <div className="border-t border-app-stroke my-1"></div>
            
            {/* Theme Toggle Section */}
            <div className="px-4 py-2">
              <p className="text-xs font-medium text-app-headline opacity-70 mb-2">Theme</p>
              <div className="flex space-x-1">
                <button
                  onClick={() => setTheme('light')}
                  className={`flex-1 flex items-center justify-center px-2 py-1.5 text-xs rounded transition-colors ${
                    theme === 'light' 
                      ? 'bg-app-main text-white' 
                      : 'text-app-headline hover:bg-app-background'
                  }`}
                >
                  <Sun className="w-3 h-3 mr-1" />
                  Light
                </button>
                <button
                  onClick={() => setTheme('dark')}
                  className={`flex-1 flex items-center justify-center px-2 py-1.5 text-xs rounded transition-colors ${
                    theme === 'dark' 
                      ? 'bg-app-main text-white' 
                      : 'text-app-headline hover:bg-app-background'
                  }`}
                >
                  <Moon className="w-3 h-3 mr-1" />
                  Dark
                </button>
                <button
                  onClick={() => setTheme('system')}
                  className={`flex-1 flex items-center justify-center px-2 py-1.5 text-xs rounded transition-colors ${
                    theme === 'system' 
                      ? 'bg-app-main text-white' 
                      : 'text-app-headline hover:bg-app-background'
                  }`}
                >
                  <Monitor className="w-3 h-3 mr-1" />
                  System
                </button>
              </div>
            </div>

            <div className="border-t border-app-stroke my-1"></div>
            
            <button
              onClick={handleSignOut}
              className="w-full flex items-center px-4 py-2 text-sm text-app-highlight hover:bg-app-background transition-colors"
            >
              <LogOut className="w-4 h-4 mr-3" />
              Sign Out
            </button>
          </div>
        </div>
      )}
    </div>
  )
}