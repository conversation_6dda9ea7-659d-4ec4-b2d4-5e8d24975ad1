import Image from "next/image"
import Link from "next/link"
import { useTheme } from "next-themes"
import { useEffect, useState } from "react"

interface LogoProps {
  size?: number
  className?: string
  href?: string
  showText?: boolean
}

export default function Logo({
  size = 200, // Increased base size for better text readability
  className = "",
  href = "/dashboard",
  showText = true // Enable text by default for better branding
}: LogoProps) {
  const { theme, resolvedTheme } = useTheme()
  const [mounted, setMounted] = useState(false)

  // Avoid hydration mismatch
  useEffect(() => {
    setMounted(true)
  }, [])

  console.log('🔍 Logo: Rendering with size:', size, 'className:', className);

  // Choose logo based on theme and showText preference
  const getLogoSrc = () => {
    if (!mounted) {
      // Return light version as default during SSR
      return "/Just-Logo.svg"
    }

    const isDark = resolvedTheme === 'dark'

    if (showText) {
      // Use Logo-White-Text.svg for dark mode, Just-Logo.svg for light mode
      return isDark ? "/Logo-White-Text.svg" : "/Just-Logo.svg"
    } else {
      // Always use Just-Logo.svg for logo-only display
      return "/Just-Logo.svg"
    }
  }

  const logoElement = (
    <div className={`flex items-center ${className}`}>
      <Image
        src={getLogoSrc()}
        alt="BuddyChip Logo"
        width={size}
        height={size}
        className="flex-shrink-0 hover:scale-105 transition-transform duration-200 w-auto h-auto max-w-full"
        style={{
          maxWidth: `${size}px`,
          maxHeight: `${size}px`,
        }}
        priority
      />
    </div>
  )

  if (href) {
    return (
      <Link href={href} className="hover:opacity-90 transition-opacity">
        {logoElement}
      </Link>
    )
  }

  return logoElement
}